{"extends": "tslint:recommended", "rules": {"array-type": false, "arrow-parens": false, "ban-types": false, "deprecation": {"severity": "warn"}, "component-class-suffix": true, "contextual-lifecycle": true, "directive-class-suffix": true, "import-blacklist": [true, "rxjs/Rx"], "interface-name": false, "max-classes-per-file": false, "max-line-length": [true, 140], "member-access": false, "member-ordering": [true, {"order": ["public-static-field", "private-static-field", "public-instance-field", "private-instance-field", "public-constructor", "private-constructor", "public-instance-method", "protected-instance-method", "private-instance-method"]}], "no-consecutive-blank-lines": false, "no-console": [true, "debug", "info", "time", "timeEnd", "trace"], "no-empty": false, "no-inferrable-types": [true, "ignore-params"], "no-non-null-assertion": true, "no-redundant-jsdoc": false, "no-string-literal": false, "no-switch-case-fall-through": true, "no-use-before-declare": true, "no-var-requires": false, "object-literal-key-quotes": false, "object-literal-shorthand": false, "object-literal-sort-keys": false, "ordered-imports": false, "prefer-const": false, "quotemark": [true, "single"], "trailing-comma": false, "no-conflicting-lifecycle": true, "no-host-metadata-property": true, "no-input-rename": true, "no-inputs-metadata-property": true, "no-output-native": true, "no-output-on-prefix": true, "no-output-rename": true, "no-outputs-metadata-property": true, "template-banana-in-box": true, "template-no-negated-async": true, "use-lifecycle-interface": true, "use-pipe-transform-interface": true, "variable-name": false, "semicolon": [true, "always", "strict-bound-class-methods"], "object-curly-spacing": ["error", "always"], "array-bracket-spacing": ["error", "always"], "import-destructuring-spacing": true}, "rulesDirectory": ["codelyzer", "rxjs-tslint"]}