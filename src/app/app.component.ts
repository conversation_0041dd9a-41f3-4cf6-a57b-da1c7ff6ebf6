import { AfterViewInit, Component, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { Meta } from '@angular/platform-browser';
import { NavigationCancel, NavigationEnd, NavigationError, Router } from '@angular/router';
import { SwBrowserTitleService, SwHubConfigService, SwHubEntityService, SwHubInitService } from '@skywind-group/lib-swui';
import { of, Subject } from 'rxjs';
import { switchMap, takeUntil, tap } from 'rxjs/operators';
import { APP_VERSION } from './app.constants';
import { BaThemeSpinner } from './common/services/baThemeSpinner/baThemeSpinner.service';

@Component({
  selector: 'app',
  encapsulation: ViewEncapsulation.None,
  templateUrl: './app.component.html',
})
export class AppComponent implements OnInit, AfterViewInit, OnDestroy {
  private readonly destroyed = new Subject<void>();

  constructor(private readonly _spinner: BaThemeSpinner,
    private router: Router,
    private browserTitleService: SwBrowserTitleService,
    meta: Meta,
    private readonly hubService: SwHubInitService) {
    meta.addTag({
      name: 'version',
      content: APP_VERSION
    });
  }

  public ngOnInit(): void {
    this.router.events.pipe(
      takeUntil(this.destroyed)
    ).subscribe(event => {
      if (event instanceof NavigationError) {
        this.router.navigate(['/pages']);
      }

      if (event instanceof NavigationEnd || event instanceof NavigationCancel || event instanceof NavigationError) {
        this._spinner.hide();
      }
    });

    this.browserTitleService.setupTitles('Casino');
  }

  public ngAfterViewInit(): void {
    this.hubService.init();
    this._spinner.hide();
  }

  onSelectClick(path: string = '') {
    this.router.navigate(['/pages/business-management/entities/setup', path, 'p']);
  }

  ngOnDestroy(): void {
    this.destroyed.next();
    this.destroyed.complete();
  }
}
