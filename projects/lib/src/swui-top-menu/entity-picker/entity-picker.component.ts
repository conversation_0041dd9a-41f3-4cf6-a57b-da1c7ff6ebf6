import {
  ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatMenuTrigger } from '@angular/material/menu';
import { combineLatest, Observable, Subject, timer } from 'rxjs';
import { map, share, take, takeUntil } from 'rxjs/operators';
import { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';
import { SwHubBriefEntity, SwHubEntityItem } from '../../services/sw-hub-entity/sw-hub-entity.model';
import { ExtendedEntity, SwHubEntityService } from '../../services/sw-hub-entity/sw-hub-entity.service';
import { SwHubInitService } from '../../services/sw-hub-init/sw-hub-init.service';

@Component({
  selector: 'lib-swui-entity-picker',
  templateUrl: './entity-picker.component.html',
  styleUrls: ['./entity-picker.component.scss'],
})
export class EntityPickerComponent implements OnInit, OnDestroy {
  @Input() showSearch = false;
  @Input() menuClass = 'entity-picker-menu';
  @Input() searchPlaceholder = 'Search';
  @Output() settingsClick = new EventEmitter<string>();

  entity?: SwHubBriefEntity | null;
  selected?: { name?: string, id?: string, path?: string } | null;
  items$: Observable<SwHubEntityItem[]> | undefined;
  entities: ExtendedEntity[] = [];
  searchInputControl = new FormControl();
  isSettingsDisabled = true;

  @ViewChild('pickerTrigger') pickerTrigger?: MatMenuTrigger;
  @ViewChild('search') searchRef?: ElementRef;

  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly entityService: SwHubEntityService,
               private readonly hubService: SwHubInitService,
               private readonly cdr: ChangeDetectorRef,
               private readonly authService: SwHubAuthService
  ) {
    this.items$ = this.entityService.items$.pipe(
      takeUntil(this.destroyed$),
      share(),
    );

    this.items$.subscribe(() => {
      if (Array.isArray(this.entityService.entities) && this.entityService.entities.length) {
        this.entityService.expandedEntities.clear();
        this.toggleCollapse(this.entityService.entities[0].id);
      }
    });
  }

  ngOnInit(): void {
    this.searchInputControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(
      searchString => {
        if (searchString) {
          this.entities = this.entityService.entities.filter(( option: SwHubEntityItem ) => {
            return option.name && option.name.toLowerCase().indexOf(searchString.toLowerCase()) > -1;
          });

          this.entityService.expandedEntities.clear();
          this.entities?.forEach(entity => this.expandToRoot(entity.id));
          this.entities = this.entityService.entities.filter(item => this.entityService.expandedEntities.get(item.id));
          this.entityService.foundedEntities = this.entities;
        } else {
          this.entityService.expandedEntities.clear();
          this.toggleCollapse(this.entityService.entities[0].id);
        }
      }
    );

    this.entityService.brief$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(item => {
      this.entity = item;
      this.cdr.detectChanges();
    });

    combineLatest([this.entityService.brief$, this.entityService.itemSelected$]).pipe(
      map(( [brief, item] ) => item || brief),
      takeUntil(this.destroyed$)
    ).subscribe(item => {
      if (item && item.id !== this.selected?.id) {
        this.hubService.sendEntityId(item.id as string);
      }

      this.selected = item;
      this.isSettingsDisabled = item?.path === ':' && this.authService.isSuperAdmin;
      this.cdr.detectChanges();
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get isReseller(): boolean {
    if (!this.entity) {
      return false;
    }
    return this.entity.type === 'entity';
  }

  onSettingsClick( event: MouseEvent ) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    this.settingsClick.emit(this.selected?.path);
  }

  select( item: SwHubEntityItem | undefined ): void {
    if (item !== undefined) {
      this.hubService.sendEntityId(item.id);
      this.searchInputControl.setValue('');
      this.entityService.use(item.id, true);
    }

    this.pickerTrigger?.closeMenu();
  }

  stopPropagation( event: Event ) {
    event.stopPropagation();
  }

  closeChildren( current: string ) {
    this.entityService.expandedEntities.set(current, false);

    if (this.entityService.entitiesObject) {
      const { children } = this.entityService.entitiesObject[current];

      children.forEach(( id: string ) => {
        this.closeChildren(id);
      });
    }
  }

  expandToRoot( id: string ): any {
    if (id) {
      this.entityService.expandedEntities.set(id, true);

      if (this.entityService.entitiesObject) {
        return this.expandToRoot(this.entityService.entitiesObject[id].parentId);
      }
    }
  }

  toggleCollapse( id: string ) {
    const isCollapsed = !this.entityService.expandedEntities.get(id);

    if (isCollapsed) {
      this.entityService.expandedEntities.set(id, true);
    } else {
      this.closeChildren(id);
    }

    this.entities = this.searchInputControl.value ?
      this.entityService.foundedEntities.filter(( item: any, index: number ) => {
        return this.entityService.expandedEntities.get(item.id) || this.entityService.expandedEntities.get(item.parentId) || !index;
      }) :
      this.entityService.entities.filter(( item: any, index: number ) => {
        return this.entityService.expandedEntities.get(item.id) || this.entityService.expandedEntities.get(item.parentId) || !index;
      });
  }

  onItemClick( event: Event, row: ExtendedEntity ) {
    this.stopPropagation(event);

    if (row.level !== 0) {
      this.toggleCollapse(row.id);
    }
  }

  onClosed() {
    this.searchInputControl.setValue(null);
  }

  isEntityExpanded( id: string ): boolean {
    return !!this.entityService.expandedEntities.get(id);
  }

  onOpen() {
    if (this.selected?.id) {
      this.expandToRoot(this.selected.id);
      this.entities = this.entityService.entities.filter(( item: any, index: number ) => {
        return this.entityService.expandedEntities.get(item.id) || this.entityService.expandedEntities.get(item.parentId) || !index;
      });
      timer(100)
        .pipe(take(1))
        .subscribe(() => {
          if (this.selected?.id) {
            const el = document.getElementById(this.selected.id);
            el?.scrollIntoView({ block: 'center' });
          }
        });
    }
  }
}
