import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';
import { HubMenuItem } from '../swui-top-menu.model';
import { SwHubMessageModuleConfig } from '../../services/sw-hub-init/sw-hub-init.model';
import { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';
import { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';

@Component({
  selector: 'lib-swui-hub-selector',
  templateUrl: './hub-selector.component.html',
  styleUrls: ['./hub-selector.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HubSelectorComponent {
  readonly hubs: HubMenuItem[];
  readonly activeHub: HubMenuItem | undefined;

  constructor( @Inject(SWUI_HUB_MESSAGE_CONFIG) settings: SwHubMessageModuleConfig,
               private readonly config: SwHubConfigService,
               private readonly auth: SwHubAuthService ) {
    this.hubs = Object.entries(this.config.hubs || {})
      .filter(( [, { permission }] ) => {
        const permissions = Array.isArray(permission) ? permission : permission ? [permission] : [];
        return permission ? this.auth.allowedTo(permissions) : true;
      })
      .reduce<HubMenuItem[]>(( result, [id, { cssClass, name, url }] ) => {
        if (name && url) {
          return [
            ...result,
            { id, name, url, cssClass }
          ];
        }
        return result;
      }, []);
    this.activeHub = this.hubs.find(( { id } ) => id === settings.name);
  }
}
