import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { take } from 'rxjs/operators';
import { AppSettings } from '../../services/settings/app-settings';
import { PERMISSIONS_NAMES } from '../../services/sw-hub-auth/permissions';
import { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';
import { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';
import { SwuiSettingsDialogComponent } from '../settings-dialog/swui-settings-dialog.component';

export const MASTER_ID = '1';

@Component({
  selector: 'lib-swui-user-menu',
  templateUrl: './user-menu.component.html',
  styleUrls: ['./user-menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UserMenuComponent {
  @Input() username: string | undefined;
  @Input() entityKey: string | undefined;
  @Input() hasTwoFactor: string | undefined;
  @Output() languageChanges = new EventEmitter<string>();
  @Output() settingsChanges = new EventEmitter<AppSettings>();
  @Output() logout = new EventEmitter<void>();

  constructor( private readonly dialog: MatDialog,
               private readonly configService: SwHubConfigService,
               private readonly auth: SwHubAuthService,
  ) {
  }

  showSettings() {
    this.dialog.open(SwuiSettingsDialogComponent, {
      width: '700px'
    }).afterClosed().pipe(
      take(1)
    ).subscribe(settings => {
      if (settings) {
        this.settingsChanges.emit(settings);
      }
    });
  }

  twoFactorSettings() {
    location.href = `${this.configService.loginUrl?.replace('/login', '')}/twofactorsettings`;
  }

  changePasswordAllowed(): boolean {
    return this.entityKey !== MASTER_ID && this.auth.allowedTo([PERMISSIONS_NAMES.KEYENTITY_USER_CHANGE_PASSWORD]);
  }

  changePassword() {
    location.href = `${this.configService.loginUrl?.replace('/login', '')}/changepassword`;
  }
}
