<h2 mat-dialog-title>{{ 'SETTINGS.title' | translate }}</h2>
<mat-dialog-content class="mat-typography">
  <form [formGroup]="form" fxLayout="column" fxFlex.gt-md="50" fxFlex="100">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'SETTINGS.timezone' | translate }}</mat-label>
      <lib-swui-select [showSearch]="true" [data]="timezoneOptions" formControlName="timezoneName">
      </lib-swui-select>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>{{ 'SETTINGS.pageSize' | translate }}</mat-label>
      <mat-select formControlName="pageSize">
        <mat-option *ngFor="let option of pageSizeOptions" [value]="option">{{option}}</mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>{{ 'SETTINGS.dateFormat' | translate }}</mat-label>
      <mat-select formControlName="dateFormat">
        <mat-option *ngFor="let option of dateFormatOptions" [value]="option.id">{{option.text}}</mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>{{ 'SETTINGS.timeFormat' | translate }}</mat-label>
      <mat-select formControlName="timeFormat">
        <mat-option *ngFor="let option of timeFormatOptions" [value]="option.id">{{option.text}}</mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>{{ 'SETTINGS.currencyFormat' | translate }}</mat-label>
      <mat-select formControlName="currencyFormat">
        <mat-option *ngFor="let option of currencyFormatOptions" [value]="option.id">
          {{option.text}}
          <span *ngIf="option.primary">{{'SETTINGS.currencyFormatDefault' | translate}}</span>
        </mat-option>
      </mat-select>
    </mat-form-field>
  </form>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button color="primary" class="mat-button-md" [mat-dialog-close]="null">
    {{ 'SETTINGS.decline' | translate }}
  </button>
  <button mat-stroked-button color="primary" class="mat-button-md" [disabled]="hasDefaultValues"
          (click)="onReset($event)">
    {{ 'SETTINGS.reset' | translate }}
  </button>
  <button mat-flat-button class="mat-button-md" [disabled]="!form.valid || !form.dirty" color="primary"
          [mat-dialog-close]="form.getRawValue()">
    {{ 'SETTINGS.apply' | translate }}
  </button>
</mat-dialog-actions>
