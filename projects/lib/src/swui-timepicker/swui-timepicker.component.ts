import { Component, forwardRef, HostBinding, HostListener, Input, OnDestroy, OnInit } from '@angular/core';
import { ControlValueAccessor, FormControl, FormGroup, NG_VALUE_ACCESSOR } from '@angular/forms';
import * as moment from 'moment';
import { Subject, timer } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';

import { SwuiTimepickerInterface, SwuiTimepickerTimeDisableLevel } from './swui-timepicker.interface';

function numList( count: number ): string[] {
  const arr = [];
  for (let i = 0; i < count; i++) {
    arr.push(i.toString().padStart(2, '0'));
  }
  return arr;
}

const HOURS = numList(24);
const MINUTES = numList(60);
const SECONDS = numList(60);

@Component({
  selector: 'lib-swui-timepicker',
  templateUrl: './swui-timepicker.component.html',
  styleUrls: ['./swui-timepicker.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SwuiTimepickerComponent),
      multi: true
    },
  ],
})
export class SwuiTimepickerComponent implements ControlValueAccessor, OnInit, OnDestroy {
  hours = HOURS;
  minutes = MINUTES;
  seconds = SECONDS;
  minTimes = {
    hours: 0,
    minutes: 0,
    seconds: 0,
  };
  maxTimes = {
    hours: 23,
    minutes: 59,
    seconds: 59,
  };

  @Input() disable: SwuiTimepickerTimeDisableLevel = {
    hour: false,
    minute: false,
    second: false,
  };
  @Input() format: 'default' | 'minutes' | 'seconds' = 'default';

  @Input() set minTime( val: string ) {
    this._minTime = val;
    this.setMinTime();
  }

  @Input() set maxTime( val: string ) {
    this._maxTime = val;
    this.setMaxTime();
  }

  @Input() set timeDisableLevel( value: SwuiTimepickerTimeDisableLevel ) {
    if (value) {
      this.disable.hour = value.hour === false;
      this.disable.minute = value.minute === false;
      this.disable.second = value.second === false;
    }
  }

  @HostBinding('attr.tabindex')
  tabindex = 0;

  readonly form: FormGroup;
  onChange: ( _: any ) => void = (() => {
  });

  private readonly destroyed$ = new Subject<void>();
  private _minTime = '';
  private _maxTime = '';

  constructor() {
    this.form = new FormGroup({
      hour: new FormControl(0),
      minute: new FormControl(0),
      second: new FormControl(0)
    });
  }

  onTouched: any = () => {
  };

  @HostListener('blur') onblur() {
    this.onTouched();
  }

  ngOnInit(): void {
    this.form.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      this.setMinTime();
      this.setMaxTime();
      this.onChange(this.transformFrom(this.form.value));
    });

    this.setMinTime();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  writeValue( value: SwuiTimepickerInterface | number | null | undefined ) {
    if (typeof value === 'undefined') {
      return;
    }
    this.form.setValue(this.transformValue(value), { emitEvent: false });
  }

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( isDisabled: boolean ): void {
    isDisabled ? this.form.disable() : this.form.enable();
  }

  private transformValue( val: SwuiTimepickerInterface | number | null ): Partial<SwuiTimepickerInterface> {
    if (val === null) {
      return {
        hour: 0,
        minute: 0,
        second: 0
      };
    }
    if (typeof val !== 'number') {
      return val;
    }
    if (this.format === 'minutes') {
      return {
        hour: Math.floor(val / 60),
        minute: val % 60,
        second: 0,
      };
    }
    return {
      hour: Math.floor(val / 3600),
      minute: Math.floor((val % 60) / 60),
      second: val % 3600,
    };
  }

  private transformFrom( val: SwuiTimepickerInterface | undefined ): SwuiTimepickerInterface | number | undefined {
    if (typeof val === 'undefined') {
      return undefined;
    }
    switch (this.format) {
      case 'minutes':
        return val.hour * 60 + val.minute;
      case 'seconds':
        return val.hour * 60 * 60 + val.minute * 60 + val.second;
      default:
        return val;
    }
  }

  private setMaxTime() {
    const time = moment(this._maxTime, 'hh:mm:ss');
    const { hour, minute, second } = this.form.getRawValue();

    const date = moment(`${hour}:${minute}:${second}`, 'hh:mm:ss');

    const minutes = time.diff(date, 'hours') > 0 ? 59 : time.minutes();
    const seconds = time.diff(date, 'minutes') > 0 ? 59 : time.seconds();

    if (date.diff(time, 'seconds') > 0) {
      timer(0)
        .pipe(take(1))
        .subscribe(() => {
          this.form.patchValue({
            hour: time.hours(),
            minute: time.minutes(),
            second: time.seconds()
          });
        });
    }

    this.maxTimes = {
      hours: time.hours(),
      minutes,
      seconds
    };
  }

  private setMinTime() {
    const time = moment(this._minTime, 'hh:mm:ss');
    const { hour, minute, second } = this.form.getRawValue();

    const date = moment(`${hour}:${minute}:${second}`, 'hh:mm:ss');

    const minutes = hour > time.hours() ? 0 : time.minutes();
    const seconds = date.diff(time, 'minutes') > 0 ? 0 : time.seconds();

    if (date.diff(time, 'seconds') < 0) {
      timer(0)
        .pipe(take(1))
        .subscribe(() => {
          this.form.patchValue({
            hour: time.hours(),
            minute: time.minutes(),
            second: time.seconds()
          });
        });
    }

    this.minTimes = {
      hours: time.hours(),
      minutes,
      seconds
    };
  }

  get elemWidth(): number {
    return 100 / ((this.disable.hour ? 0 : 1) + (this.disable.minute ? 0 : 1) + (this.disable.second ? 0 : 1));
  }
}
