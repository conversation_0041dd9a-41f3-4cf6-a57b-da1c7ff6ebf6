import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { catchError, first, map } from 'rxjs/operators';
import { of } from 'rxjs';
import { PERMISSIONS_NAMES } from '../sw-hub-auth/permissions';

type EnvType = 'qa' | 'dev' | 'stg' | 'prod';
type LocationType = 'asia' | 'europe';

export interface HubConfigHub {
  url: string;
  name?: string;
  cssClass?: string;
  permission?: string | string[];
}

export interface HubConfigHubs {
  [key: string]: HubConfigHub;
}

abstract class BaseHubConfig {
  bridge?: string;
  loginUrl?: string;
  logoutUrl?: string;
  defaultUrl?: string;
  oauthClientId?: string;
  envName?: EnvType;
  locationName?: LocationType;
  logo?: {
    main?: string;
    solo?: string;
    white?: string;
    favicon?: string;
  };
}

export interface HubUrls extends BaseHubConfig {
  hubs?: {
    casino?: string;
    engagement?: string;
    analytics?: string;
    studio?: string;
  };
}

export abstract class HubConfig extends BaseHubConfig {
  hubs?: HubConfigHubs;
}

@Injectable()
export class SwHubConfigService extends HubConfig {



  constructor( private readonly http: HttpClient ) {
    super();
  }

  fetch(): Promise<boolean> {
    return new Promise(( resolve, reject ) => {
      this.http.get<HubUrls>('/api/config').pipe(
        first(),
        map<HubUrls, HubConfig | undefined>(config => {
          if (!config) {
            return undefined;
          }
          const { hubs, ...data } = config;
          return {
            ...data,
            hubs: {
              ...(hubs?.casino ? {
                casino: {
                  url: hubs.casino,
                  name: 'HUBS.casino',
                  cssClass: 'hub-casino',
                  permission: PERMISSIONS_NAMES.HUB_CASINO
                }
              } : {}),
              ...(hubs?.engagement ? {
                engagement: {
                  url: hubs.engagement,
                  name: 'HUBS.engagement',
                  cssClass: 'hub-engagement',
                  permission: PERMISSIONS_NAMES.HUB_ENGAGEMENT
                }
              } : {}),
              ...(hubs?.analytics ? {
                analytics: {
                  url: hubs.analytics,
                  name: 'HUBS.analytics',
                  cssClass: 'hub-analytics',
                  permission: PERMISSIONS_NAMES.HUB_ANALYTICS
                }
              } : {}),
              ...(hubs?.studio ? {
                studio: {
                  url: hubs.studio,
                  name: 'HUBS.studio',
                  cssClass: 'hub-studio',
                  permission: PERMISSIONS_NAMES.HUB_STUDIO
                }
              } : {}),
            }
          };
        }),
        catchError(err => {
          reject(err);
          return of(undefined);
        })
      ).subscribe(config => {
        Object.assign(this, config);
        resolve(true);
      });
    });
  }
}
