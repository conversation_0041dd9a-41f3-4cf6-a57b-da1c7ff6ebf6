import { Injectable } from '@angular/core';
import { isEqual, cloneDeep } from 'lodash';
import { Observable, ReplaySubject, Subject } from 'rxjs';
import { SchemaTopFilterField } from './swui-schema-top-filter.model';
import { MultiselectInputOptionData, SelectInputOptionData, SelectOptionItem, SelectTableInputOptionData } from '../swui-dynamic-form/dynamic-form.model';
import { SwuiSelectTableOption } from '../swui-select-table/swui-select-table.interface';

@Injectable()
export class SwuiTopFilterDataService {
  public onReset = new Subject<void>();
  private _displayedFilterState = new Subject<any>();
  private _appliedFilterState = new ReplaySubject<any>(1);
  private _filterFormState = new ReplaySubject(1);
  private appliedFilterValue: Object = {};
  private patched = false;

  get displayedFilter(): Observable<any> {
    return this._displayedFilterState.asObservable();
  }

  get appliedFilter(): Observable<any> {
    return this._appliedFilterState.asObservable();
  }

  get filterFormState(): Observable<any> {
    return this._filterFormState.asObservable();
  }

  setFormState( value: any ) {
    this._filterFormState.next(value);
  }

  setDisplayFilter( value: any ): void {
    this._displayedFilterState.next(value);
  }

  patchFilter(value: Object) {
    const needUpdate = Object.keys(value).every(key => this.appliedFilterValue.hasOwnProperty(key));

    if (!needUpdate) {
      return;
    }

    const newFilter = { ...this.appliedFilterValue, ...value };

    if (!isEqual(newFilter, this.appliedFilterValue)) {
      this.submitFilter(newFilter);
      this.patched = true;
    }
  }

  /**
   * Updates appliedFilter to remove values that don't exist in the provided schema
   */
  updateFilter(schema: SchemaTopFilterField[]): void {
    const updatedFilter: Record<string, any> = cloneDeep(this.appliedFilterValue);
    for (const [field, value] of Object.entries(updatedFilter)) {
      const fieldSchema = schema.find(item => item.field === field);
      let data: SelectTableInputOptionData['data'] | SelectInputOptionData['data'] | MultiselectInputOptionData['data'];
      if (fieldSchema?.type === 'select-table') {
        data = (fieldSchema as SelectTableInputOptionData).data;
      } else if (fieldSchema?.type === 'multiselect') {
        data = (fieldSchema as MultiselectInputOptionData).data;
      } else if (fieldSchema?.type === 'select') {
        data = (fieldSchema as SelectInputOptionData).data;
      }
      if (!data) {
        continue;
      }
      if (data instanceof Observable) {
        updatedFilter[field] = [];
      } else {
        const options = data as (SelectOptionItem[] | SwuiSelectTableOption[]);
        if (Array.isArray(value)) {
          // "multiselect"
          updatedFilter[field] = value.filter(item => options.some(option => item.id === option.id));
        } else if (typeof value === 'object' && value !== null) {
          // "select-table"
          if (!options.some(option => value.id === option.id)) {
            delete updatedFilter[field];
          }
        } else {
          // "select"
          if (!options.some(option => value === option.id)) {
            delete updatedFilter[field];
          }
        }
      }
    }
    if (!isEqual(updatedFilter, this.appliedFilterValue)) {
      this.submitFilter(updatedFilter);
    }
  }

  submitFilter(value: any, checkPatched?: boolean): void {
    if (!this.patched || !checkPatched) {
      this.appliedFilterValue = value;
      this.setDisplayFilter(value);
      this._appliedFilterState.next(value);
    }

    this.patched = false;
  }

  resetFilter(): void {
    this.onReset.next();
    this.submitFilter({});
  }
}
