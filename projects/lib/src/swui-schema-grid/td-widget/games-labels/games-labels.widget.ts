import { Component, Inject } from '@angular/core';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

export interface SwuiGamesLabelsTdWidgetSchema {
  classMap?: { [group: string]: string };
}

@Component({
  selector: 'lib-swui-td-game-labels-widget',
  templateUrl: './games-labels.widget.html',
})
export class SwuiTdGamesLabelsWidgetComponent {
  items: { title: string; class: string }[];

  constructor(
    @Inject(SWUI_GRID_WIDGET_CONFIG) { field, row, schema: { classMap } }: SwuiGridWidgetConfig<SwuiGamesLabelsTdWidgetSchema>
  ) {
    this.items = (row[field] as any[]).map(item => ({
      title: item.title,
      class: classMap && classMap.hasOwnProperty(item.group) ? classMap[item.group] : ''
    }));
  }
}
