import { Component, Inject } from '@angular/core';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

export interface SwuiIconTdWidgetSchema {
  td?: {
    classFn?: ( row: any, schema: SwuiIconTdWidgetSchema ) => any;
    titleFn?: ( row: any, schema: SwuiIconTdWidgetSchema ) => string;
    linkFn?: ( row: any, schema: SwuiIconTdWidgetSchema ) => string[];
    canActivateFn?: ( row: any, schema: SwuiIconTdWidgetSchema ) => boolean;
    icon?: string;
    svgIcon?: string;
    fontSet?: string;
    fontIcon?: string;
    useTranslate?: boolean;
  };
}

@Component({
  selector: 'lib-swui-td-icon-widget',
  templateUrl: './icon.widget.html',
  styleUrls: ['./icon.widget.scss']
})

export class SwuiTdIconWidgetComponent {
  readonly icon?: string;
  readonly svgIcon?: string;
  readonly fontSet?: string;
  readonly fontIcon?: string;

  readonly title: string;
  readonly canActivate: boolean;

  readonly routerLinkData: string[];
  readonly classObj: any;
  readonly useTranslate: boolean;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { row, schema, value }: SwuiGridWidgetConfig<SwuiIconTdWidgetSchema> ) {
    const activeFn = schema.td?.canActivateFn;
    const titleFn = schema.td?.titleFn;
    const linkFn = schema.td?.linkFn;
    const classFn = schema.td?.classFn;

    this.icon = schema.td?.icon ? schema.td.icon : undefined;
    this.svgIcon = schema.td?.svgIcon ? schema.td.svgIcon : undefined;
    this.fontSet = schema.td?.fontSet ? schema.td.fontSet : undefined;
    this.fontIcon = schema.td?.fontIcon ? schema.td.fontIcon : undefined;

    this.title = (titleFn && titleFn(row, schema)) || value;
    this.canActivate = (activeFn && activeFn(row, schema)) || value;

    this.routerLinkData = linkFn && linkFn(row, schema) || [];
    this.classObj = classFn && classFn(row, schema);
    this.useTranslate = schema.td && 'useTranslate' in schema.td ? schema.td?.useTranslate || false : true;
  }
}
