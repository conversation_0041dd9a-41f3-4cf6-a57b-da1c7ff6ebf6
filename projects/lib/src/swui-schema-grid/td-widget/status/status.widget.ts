import { Component, EventEmitter, Inject } from '@angular/core';
import { WidgetActionEvent } from '../td-widget';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

export interface SwuiStatusTdWidgetParamsSchema {
  td?: {
    statusList?: Status[];
    displayStatusList?: Status[];
    classMap?: { [status: string]: string };
    useTranslate?: boolean;
    readonly?: boolean;
    readonlyFn?: ( row: any, schema: { [name: string]: any } ) => boolean;
  };
}

export interface Status {
  id: string;
  code: string;
  displayName: string;
  hidden: boolean;
}

@Component({
  selector: 'lib-swui-td-status-widget',
  templateUrl: './status.widget.html',
  styleUrls: ['./status.widget.scss']
})
export class SwuiTdStatusWidgetComponent {
  availableStatuses: any[] = [];
  loading = false;

  readonly readonly: boolean;
  useTranslate = false;

  private readonly field: string;
  private readonly row: any;
  private readonly action: EventEmitter<WidgetActionEvent>;

  private readonly statusList: any[];
  private readonly displayStatusList: any[];
  private readonly statusMap: { [code: string]: Status } = {};
  private readonly colorMap: { [status: string]: string };

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { row, field, action, schema }: SwuiGridWidgetConfig<SwuiStatusTdWidgetParamsSchema> ) {
    this.field = field;
    this.row = row;
    this.action = action;

    this.statusList = schema.td?.statusList?.map(item => {
      return {
        ...item,
        displayName: item.displayName || 'Unknown'
      };
    }) || [];
    this.displayStatusList = schema.td?.displayStatusList?.map(item => {
      return {
        ...item,
        displayName: item.displayName || 'Unknown'
      };
    }) || this.statusList;
    this.statusMap = this.displayStatusList.reduce(( result, status ) => ({ ...result, [status.code]: status }), {});
    this.colorMap = schema.td?.classMap || {};
    this.readonly = schema.td?.readonly || (schema.td?.readonlyFn && schema.td.readonlyFn(row, schema)) || false;
    this.useTranslate = schema.td && 'useTranslate' in schema.td ? schema.td?.useTranslate || false : true;

    this.setAvailableStatuses();
  }

  get currentStatus(): string {
    // let statusField = this.injector.get('field');
    return this.row[this.field];
  }

  getStatusColor( status: string ): string {
    return this.colorMap.hasOwnProperty(status) ? this.colorMap[status] : 'primary';
  }

  statusClick( $event: MouseEvent, status: Status ) {
    $event.preventDefault();
    this.loading = true;
    const data = {
      field: this.field,
      row: this.row,
      payload: {
        status: status.code,
        onCompleteFn: () => {
          this.loading = false;
          this.setAvailableStatuses();
        }
      }
    };
    this.action.emit(data);
    // this.dropdown.close();
  }

  getStatusName( status: string ) {
    let statusName = 'Unknown';
    if (status !== '' && this.statusMap.hasOwnProperty(status)) {
      statusName = this.statusMap[status].displayName;
    }
    return statusName;
  }

  private setAvailableStatuses() {
    this.availableStatuses = this.statusList.filter(( { id, hidden = false } ) => id !== this.currentStatus && !hidden);
  }
}
