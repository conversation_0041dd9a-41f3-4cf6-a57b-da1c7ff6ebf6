import { Component, EventEmitter, Inject } from '@angular/core';

import { SwuiTdColorfulLabelsWidgetComponent } from '../colorful-labels/colorful-labels.widget';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

@Component({
  selector: 'lib-swui-td-jackpot-widget',
  templateUrl: './jackpot.widget.html'
})
export class SwuiTdJackpotWidgetComponent extends SwuiTdColorfulLabelsWidgetComponent {
  jackpots: { jackpotType: string, jackpotId: string }[] = [];
  protected palettes = ['pink', 'violet', 'purple', 'indigo'];

  private readonly action: EventEmitter<any>;
  private readonly field: string;
  private readonly row: any;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) config: SwuiGridWidgetConfig<{}> ) {
    super(config);
    this.action = config.action;
    this.field = config.field;
    this.row = config.row;

    if (this.row.settings && ('jackpotId' in this.row.settings)) {
      if (typeof this.row.settings.jackpotId === 'string') {
        this.row.settings.jackpotId = [this.row.settings.jackpotId];
      }
      (this.row.settings.jackpotId as string[]).forEach(( jackpotId, idx ) => {
        let jackpotType = this.row.settings.jackpotType[idx];
        this.jackpots.push({ jackpotId, jackpotType });
      });
    }
  }

  manageClicked( event: Event ) {
    event.preventDefault();
    this.action.emit({
      field: this.field,
      row: this.row,
      payload: { showManage: true }
    });
  }
}
