import { Component, Inject } from '@angular/core';
import { Truncate } from '../../pipes/truncate.interface';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

export interface SwuiStringTdWidgetSchema {
  td?: {
    truncate?: Truncate;
    nowrap?: boolean;
  };
}

@Component({
  selector: 'lib-swui-td-string-widget',
  templateUrl: './string.widget.html',
  styles: ['.no-wrap {white-space: nowrap}']
})
export class SwuiTdStringWidgetComponent {
  value: any;
  truncate: Truncate | undefined;
  nowrap = false;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { value, schema }: SwuiGridWidgetConfig<SwuiStringTdWidgetSchema> ) {
    this.value = value;
    this.truncate = schema.td && schema.td.truncate ? schema.td.truncate : undefined;
    this.nowrap = schema.td && schema.td.nowrap || false;
  }
}
