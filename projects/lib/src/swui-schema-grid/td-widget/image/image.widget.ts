import { Component, Inject } from '@angular/core';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

export interface SwuiImageTdWidgetSchema {
  td?: {
    classFn?: ( row: any, schema: SwuiImageTdWidgetSchema ) => any;
    imageFn?: ( row: any, schema: SwuiImageTdWidgetSchema ) => string;
    alt?: string;
  };
}

@Component({
  selector: 'lib-swui-td-image-widget',
  templateUrl: './image.widget.html',
  styleUrls: ['./image.widget.scss']
})

export class SwuiTdImageWidgetComponent {
  image?: string;
  alt?: string;
  classObj: any;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { schema, row, value }: SwuiGridWidgetConfig<SwuiImageTdWidgetSchema> ) {
    const classFn = schema.td?.classFn;
    const imageFn = schema.td?.imageFn;

    this.classObj = classFn && classFn(row, schema);
    this.image = (imageFn && imageFn(row, schema)) || value;
    this.alt = schema.td?.alt || '';
  }
}
