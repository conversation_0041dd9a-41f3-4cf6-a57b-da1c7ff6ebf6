import { Component, ElementRef, EventEmitter, Inject, ViewChild } from '@angular/core';
import { WidgetActionEvent } from '../td-widget';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';


export interface SwuiPercentEditableWidgetSchema {
  delimiter?: string;
  fractionCount?: number;
  td?: {
    formatted?: boolean; // 'true' means that 1 is 1%; 'false' means that 1 is 100%
    remoteSave?: boolean;
    showUndefinedWarning?: boolean;
  };
}

type WidgetMode = 'edit' | 'view';

const MIN = 0;
const MAX = 100;
const STEP = 0.1;

@Component({
  selector: 'lib-swui-td-percent-editable-widget',
  templateUrl: './percent-editable.widget.html',
  styleUrls: ['./percent-editable.widget.scss']
})
export class SwuiTdPercentEditableWidgetComponent {
  @ViewChild('input') input?: ElementRef;

  value: any;
  mode: WidgetMode = 'view';
  loading = false;

  readonly showUndefinedWarning: boolean;
  readonly delimiter?: string;
  readonly fractionCount: number;

  private readonly field: string;
  private readonly row: any;
  private readonly action: EventEmitter<WidgetActionEvent>;

  private readonly formatted: boolean; // 'true' means that 1 is 1%; 'false' means that 1 is 100%
  private readonly remoteSave: boolean;
  private readonly initialValue: number;

  private _percentValue: number;

  constructor(
    @Inject(SWUI_GRID_WIDGET_CONFIG) { row, field, action, schema, value }: SwuiGridWidgetConfig<SwuiPercentEditableWidgetSchema>
  ) {
    this.action = action;
    this.field = field;
    this.row = row;

    this.formatted = schema.td && 'formatted' in schema.td ? schema.td.formatted || false : true;
    this.remoteSave = schema.td?.remoteSave || false;
    this.showUndefinedWarning = schema.td?.showUndefinedWarning || false;
    this.delimiter = schema.delimiter;
    this.fractionCount = schema.fractionCount || 0;

    this.value = value || 0;
    if (!this.formatted) {
      this.value *= 100;
    }
    this._percentValue = parseFloat(this.sanitizeValue(this.value).toFixed(this.fractionCount));
    this.initialValue = this.percentValue;
  }

  set percentValue( value: number ) {
    this._percentValue = parseFloat(this.sanitizeValue(value).toFixed(this.fractionCount));
  }

  get percentValue(): number {
    return this._percentValue;
  }

  onEditClick( event: MouseEvent ) {
    event.preventDefault();
    this.mode = 'edit';
  }

  valueDown() {
    this.percentValue -= STEP;
  }

  valueUp() {
    this.percentValue += STEP;
  }

  valueChanged() {
    if (this.input) {
      this.percentValue = this.input.nativeElement.value;
    }
  }

  saveChanges() {
    if (this.initialValue === this.percentValue) {
      this.mode = 'view';
      return;
    }
    this.value = this.formatted ? this.percentValue : this.percentValue / 100;
    if (this.remoteSave) {
      this.loading = true;
    }
    this.action.emit({
      field: this.field,
      row: this.row,
      payload: {
        value: this.percentValue,
        onCompleteFn: () => {
          this.loading = false;
          this.mode = 'view';
        }
      }
    });
  }

  onKeyPress() {
    this.valueChanged();
    this.saveChanges();
  }

  private sanitizeValue( value: string | number ): number {
    if (typeof value === 'string') {
      value = +parseFloat(value).toFixed(this.fractionCount);
    }
    if (isNaN(value)) {
      value = 0;
    }
    if (value > MAX) {
      value = MAX;
    }
    if (value < MIN) {
      value = MIN;
    }
    return value;
  }
}
