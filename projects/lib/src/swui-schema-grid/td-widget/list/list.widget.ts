import { Component, Inject } from '@angular/core';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

export interface SwuiListTdWidgetSchema {
  td?: {
    arrayKey?: string;
    valueFn?: ( row: any, schema: { [name: string]: any } ) => any;
  };
}

@Component({
  selector: 'lib-swui-td-list-widget',
  templateUrl: './list.widget.html',
  styleUrls: ['./list.widget.scss']
})
export class SwuiTdListWidgetComponent {
  value: any;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { schema, row, value }: SwuiGridWidgetConfig<SwuiListTdWidgetSchema> ) {
    this.value = (schema.td?.valueFn && schema.td.valueFn(row, schema)) || value;
    const key: any = schema.td?.arrayKey ? schema.td.arrayKey : 'id';

    this.value = this.value.reduce(( acc: any[], curr: { [key: string]: any; } ) => {
      if (curr && typeof curr === 'object') {
        if (curr[key]) {
          acc.push(curr[key]);
        }
      } else {
        acc.push(curr);
      }
      return acc;
    }, []);
  }
}
