import { Component, Inject } from '@angular/core';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';


export interface SwuiInactivityTdWidgetSchema {
  td?: {
    valueFn?: ( row: any, schema: { [name: string]: any } ) => any;
  };
}

@Component({
  selector: 'lib-swui-td-inactivity-widget',
  templateUrl: './inactivity.widget.html',
  styleUrls: ['./inactivity.widget.scss']
})
export class SwuiTdInactivityWidgetComponent {
  readonly value: boolean;
  readonly row: any;
  readonly field: any;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { schema, row, field }: SwuiGridWidgetConfig<SwuiInactivityTdWidgetSchema> ) {
    this.row = row;
    this.field = field;

    const valueFn = schema.td?.valueFn;
    this.value = schema.td && 'valueFn' in schema.td ? (valueFn && valueFn(row, schema)) : this.row[this.field];
  }

}
