import { Component, Inject } from '@angular/core';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

export interface SwuiPercentWidgetSchema {
  delimiter?: string;
  fractionCount?: number;
  td?: {
    formatted?: boolean; // 'true' means that 1 is 1%; 'false' means that 1 is 100%
  };
}

@Component({
  selector: 'lib-swui-td-percent-widget',
  templateUrl: './percent.widget.html'
})
export class SwuiTdPercentWidgetComponent {
  readonly delimiter: string | undefined;
  readonly fractionCount: number;
  readonly value: number;

  constructor(
    @Inject(SWUI_GRID_WIDGET_CONFIG) { value, schema: { delimiter, fractionCount, td } }: SwuiGridWidgetConfig<SwuiPercentWidgetSchema>
  ) {
    this.delimiter = delimiter;
    this.fractionCount = fractionCount || 0;
    this.value = value || 0;
    const formatted = td && 'formatted' in td ? td.formatted : true;
    if (!formatted) {
      this.value *= 100;
    }
  }
}
