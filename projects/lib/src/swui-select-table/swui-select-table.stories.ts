import { FormsModule } from '@angular/forms';
import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { APP_BASE_HREF } from '@angular/common';
import { SwuiSelectTableModule } from './swui-select-table.module';
import { SwuiSelectTableOption } from './swui-select-table.interface';
import { I18nModule } from '../i18n.module';
import { action } from '@storybook/addon-actions';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCardModule } from '@angular/material/card';


const testData: SwuiSelectTableOption[] = [
  { id: '1', text: 'Solo Option1', addData: 'Add 1' },
  { id: '2', text: 'Test Option2', addData: 'Add 2' },
  { id: '3', text: 'Option3', addData: 'Add 3' },
  { id: '4', text: 'Test Option4', addData: 'Add 4' },
  { id: '5', text: 'Option5', addData: 'Add 5' }
];

const EN = require('./locale.json');

const multiple = `
  <mat-card style="margin: 32px">
    <lib-swui-select-table
      (ngModelChange)="change($event)"
      [data]="data"
      [ngModel]="value"
      [showSearch]="showSearch"
      [disabled]="disabled"
      [columns]="columns"
      [disableEmptyOption]="disableEmptyOption"
      [disableAllOption]="disableAllOption">
    </lib-swui-select-table>
  </mat-card>
`;

storiesOf('Forms/Select-table', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        SwuiSelectTableModule,
        MatFormFieldModule,
        FormsModule,
        MatCardModule,
      ],
      providers: [
        { provide: APP_BASE_HREF, useValue: '/' },
      ]
    })
  )
  .add('default', () => ({
    template: multiple,
    props: {
      data: testData,
      showSearch: true,
      columns: [{
        field: 'text',
        name: 'First'
      }, {
        field: 'addData',
        name: 'Second'
      }],
      change: action('change')
    }
  }))
  .add('disabled', () => ({
    template: multiple,
    props: {
      data: testData,
      showSearch: true,
      disabled: true,
      columns: [{
        field: 'text',
        name: 'First'
      }, {
        field: 'addData',
        name: 'Second'
      }],
      change: action('change')
    }
  }));
