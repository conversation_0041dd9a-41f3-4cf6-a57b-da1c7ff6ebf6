import { FocusMonitor } from '@angular/cdk/a11y';
import { coerceArray, coerceBooleanProperty } from '@angular/cdk/coercion';
import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import {
  AfterContentInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, HostBinding, Input, isDevMode,
  OnInit, Optional, Self, ViewChild
} from '@angular/core';
import { FormControl, FormGroupDirective, NgControl } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { MatFormFieldControl } from '@angular/material/form-field';
import { MatMenuTrigger } from '@angular/material/menu';
import { TranslateService } from '@ngx-translate/core';
import { ReplaySubject } from 'rxjs';
import { filter, map, take, takeUntil } from 'rxjs/operators';
import { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';
import { SwuiSelectOption } from './swui-select.interface';

const CONTROL_NAME = 'lib-swui-select';
let nextUniqueId = 0;

@Component({
  selector: 'lib-swui-select',
  templateUrl: './swui-select.component.html',
  styleUrls: ['./swui-select.component.scss'],
  providers: [{ provide: MatFormFieldControl, useExisting: SwuiSelectComponent }],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SwuiSelectComponent extends SwuiMatFormFieldControl<string | string[] | undefined> implements OnInit, AfterContentInit {
  @Input() searchPlaceholder = 'Search';
  @Input() showSearch = false;
  @Input() startSearchLength = 0;
  @Input() emptyOptionPlaceholder = 'None';
  @Input() disableEmptyOption = false;
  @Input() disableAllOption = false;

  @Input()
  set multiple( value: boolean ) {
    const newMultiple = coerceBooleanProperty(value);
    if (newMultiple !== this._multiple) {
      if (isDevMode() && this._contentInitialized) {
        throw new Error('Cannot change `multiple` mode of `lib-swui-select` after initialization.');
      }
      this._multiple = newMultiple;
      this.itemHeight = newMultiple ? 48 : 42;
    }
  }

  get multiple(): boolean {
    return this._multiple;
  }

  @Input()
  set data( val: SwuiSelectOption[] ) {
    this._data = val || [];
    this.options = this._data;

    if (this.multiple && !this.disableAllOption) {
      this.allChecked = this.value?.length === this.enabledData.length;
    }

    this.options?.forEach(option => option.state = {
      checked: this.value?.includes(option.id),
    });

    if (Array.isArray(this.options) && this.options.length) {
      this.onDataReceived.next(this.options);
    }
  }

  get data(): SwuiSelectOption[] {
    return this._data;
  }

  @Input()
  set value( val: string | string[] | undefined ) {
    this.patchSelectControl(val);
  }

  get value(): string | string[] | undefined {
    return this.multiple ?
      this.selectControl.value?.map(( v: SwuiSelectOption ) => v.id) :
      this.selectControl.value && this.selectControl.value[0]?.id;
  }

  get empty() {
    return !this.selectControl.value?.length;
  }

  get viewportHeight(): number {
    let length = this.data.length;
    if (!this.multiple && !this.disableEmptyOption) {
      length += 1;
    }
    length = Math.floor(length / 5) && 5 || length % 5;
    if (length === 5) {
      if (this.showSearch) {
        length -= 1;
      }
      if (this.multiple) {
        length -= 1;
      }
    }
    return length * this.itemHeight;
  }

  allChecked = false;
  options: SwuiSelectOption[] = [];

  itemHeight = 48;

  onDataReceived = new ReplaySubject<SwuiSelectOption[]>(1);

  readonly controlType = CONTROL_NAME;
  readonly triggerInputControl = new FormControl();
  readonly searchControl = new FormControl();
  readonly selectControl = new FormControl();

  @ViewChild('trigger') trigger?: MatMenuTrigger;
  @ViewChild('search') searchRef?: ElementRef;
  @ViewChild(CdkVirtualScrollViewport, { static: false }) virtualScroll?: CdkVirtualScrollViewport;

  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;

  @HostBinding('class.floating')
  get shouldLabelFloat() {
    return this.triggerInputControl.value;
  }

  private _contentInitialized = false;
  private _multiple = false;
  private _data: SwuiSelectOption[] = [];

  private previousSelected: string[] | null = null;

  constructor( fm: FocusMonitor,
               elRef: ElementRef<HTMLElement>,
               @Optional() @Self() ngControl: NgControl,
               @Optional() parentFormGroup: FormGroupDirective,
               errorStateMatcher: ErrorStateMatcher,
               private readonly cd: ChangeDetectorRef,
               private readonly translate: TranslateService
  ) {
    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);
  }

  ngOnInit(): void {
    this.searchControl.valueChanges.pipe(
      filter<string>(data => this.showSearch && (data.length >= (this.startSearchLength || 0) || data.length === 0)),
      map<string, string>(searchString => searchString.toLowerCase()),
      takeUntil(this.destroyed$)
    ).subscribe(search => {
      this.previousSelected = this.multiple && search ? this.selectControl.value : null;
      this.options = this.data.filter(option => {
        return option.text && option.text.toLowerCase().indexOf(search) > -1;
      });
      this.cd.markForCheck();
    });

    this.selectControl.valueChanges.pipe(
      map<SwuiSelectOption[], SwuiSelectOption[]>(val => {
        if (this.multiple && Array.isArray(this.previousSelected) && Array.isArray(val)) {
          const previousSelected = this.previousSelected.filter(id => !this.options.some(option => option.id === id));
          const values = this.enabledData.filter(( { id } ) => previousSelected.some(item => item === id));
          return [...values, ...val];
        }

        return val;
      }),
      map<SwuiSelectOption[], string[]>(val => val.map(item => item ? item.id : '')),
      takeUntil(this.destroyed$)
    ).subscribe(val => {
      this.setVisibleValue(val);

      this.onChange(this.multiple ? val : val[0] || null);

      if (this.multiple && !this.disableAllOption) {
        this.allChecked = this.enabledData.every(({id}) => this.value?.includes(id));
      }
      this.cd.detectChanges();
    });
  }

  ngAfterContentInit(): void {
    this._contentInitialized = true;
  }

  setVisibleValue( val: string[], options?: SwuiSelectOption[] ) {
    if (options) {
      this.patchTriggerInputControl(val, options);
    } else {
      this.onDataReceived
        .pipe(
          take(1),
        )
        .subscribe(
          data => {
            this.patchTriggerInputControl(val, data);
          });
    }
  }

  patchTriggerInputControl( val: string[], options: SwuiSelectOption[] ) {
    let option = options?.find(opt => opt.id === val[0]);

    let text = option ?
      this.translate.instant(option.text) :
      val[0];

    let visibleValue = val?.length > 1 ?
      `${text} (+${val.length - 1} ${this.translate.instant('ALL.more')})` :
      text;

    this.triggerInputControl.patchValue(visibleValue);
  }

  onContainerClick( event: MouseEvent ): void {
    event.stopPropagation();

    if (this.elRef && this.trigger && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {
      this.elRef.nativeElement.focus();
      this.trigger.openMenu();
    }
  }

  writeValue( val: string | string[] | undefined ): void {
    this.patchSelectControl(val);
  }

  patchSelectControl( val: string | string[] | undefined ) {
    this.onDataReceived
      .pipe(
        take(1),
      )
      .subscribe(
        options => {
          if (this.multiple && !this.disableAllOption) {
            this.allChecked = Array.isArray(val) && val.length === this.enabledData.length;
          }

          const values = coerceArray(val || []);

          options?.forEach(option => option.state = {
            checked: values?.includes(option.id),
          });

          const value = options.filter(opt => values.includes(opt.id));

          this.selectControl.patchValue(value, { emitEvent: false });
          this.setVisibleValue(values, options);

          this.cd.detectChanges();
        }
      );
  }

  toggleAll( event?: MouseEvent ) {
    event?.preventDefault();
    event?.stopPropagation();

    this.allChecked = !this.allChecked;
    this.options.forEach(option => {
      if (!option.disabled) {
        option.state.checked = this.allChecked;
      }
    });

    let checkedOptions = this.options.filter(option => option.state.checked);

    this.selectControl.setValue(checkedOptions);
  }

  onOpened() {
    if (this.searchRef) {
      const input = this.searchRef.nativeElement as HTMLInputElement;
      input.focus();
    }

    setTimeout(() => {
      if (this.virtualScroll) {
        if (this.selectControl.value && this.selectControl.value.length) {
          this.virtualScroll.scrollToIndex(
            this.options.map(option => option.id).indexOf(this.selectControl.value[0].id));
        } else {
          this.virtualScroll.scrollToOffset(1);
        }
      }
    }, 100);
  }

  onClosed() {
    this.searchControl.reset('', { emitEvent: false });
    this.options = this.data;
    this.previousSelected = null;
  }

  onSelectMultiple( event: MouseEvent, option: SwuiSelectOption ) {
    event.preventDefault();
    event.stopPropagation();

    if (option.state) {
      option.state.checked = !option.state.checked;
    }

    this.selectControl.patchValue(this.data?.filter(item => item.state?.checked));
  }

  onSelect( option: SwuiSelectOption | null ) {
    this.options.forEach(opt => opt.state.checked = opt.id === option?.id);

    this.selectControl.patchValue(coerceArray(option || []));
  }

  stopPropagation( event: Event ) {
    event.stopPropagation();
  }

  protected onDisabledState( disabled: boolean ) {
    disabled ?
      this.triggerInputControl.disable({ emitEvent: false }) :
      this.triggerInputControl.enable({ emitEvent: false });
  }

  private get enabledData(): SwuiSelectOption[] {
    return this.data.filter(( { disabled } ) => !disabled);
  }
}
