import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { APP_BASE_HREF } from '@angular/common';
import { SwuiSelectModule } from './swui-select.module';
import { SwuiSelectOption } from './swui-select.interface';
import { I18nModule } from '../i18n.module';
import { action } from '@storybook/addon-actions';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCardModule } from '@angular/material/card';


const testData: SwuiSelectOption[] = [
  { id: '1', text: 'Solo Option1' },
  { id: '2', text: 'Test Option2' },
  { id: '3', text: 'Option3', disabled: true },
  { id: '4', text: 'Test Option4' },
  { id: '5', text: 'Option5' },
];

const EN = require('./locale.json');

const single = `
  <mat-card style="margin: 32px">
    <mat-form-field appearance="outline">
      <mat-label>
        Test label
      </mat-label>
      <lib-swui-select
        (ngModelChange)="change($event)"
        [data]="data"
        [ngModel]="value"
        [showSearch]="showSearch"
        [startSearchLength]="startSearchLength"
        [disabled]="disabled"
        [disableEmptyOption]="disableEmptyOption"
        [disableAllOption]="disableAllOption">
      </lib-swui-select>
    </mat-form-field>
  </mat-card>
`;

const multiple = `
  <mat-card style="margin: 32px">
    <mat-form-field appearance="outline">
      <mat-label>
        Test label
      </mat-label>
      <lib-swui-select
        multiple
        (ngModelChange)="change($event)"
        [data]="data"
        [ngModel]="value"
        [showSearch]="showSearch"
        [disabled]="disabled"
        [disableEmptyOption]="disableEmptyOption"
        [disableAllOption]="disableAllOption">
      </lib-swui-select>
    </mat-form-field>
  </mat-card>
`;

storiesOf('Forms/Select', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        SwuiSelectModule,
        MatFormFieldModule,
        MatCardModule,
      ],
      providers: [
        { provide: APP_BASE_HREF, useValue: '/' },
      ]
    })
  )
  .add('default', () => ({
    template: single,
    props: {
      data: testData,
      change: action('change')
    }
  }))
  .add('showSearch', () => ({
    template: single,
    props: {
      data: testData,
      showSearch: true,
      change: action('change')
    }
  }))
  .add('search with start length', () => ({
    template: single,
    props: {
      data: testData,
      showSearch: true,
      startSearchLength: 3,
      change: action('change')
    }
  }))
  .add('value', () => ({
    template: single,
    props: {
      data: testData,
      value: '2',
      change: action('change')
    }
  }))
  .add('disabled', () => ({
    template: single,
    props: {
      data: testData,
      disabled: true,
      change: action('change')
    }
  }))
  .add('disabled with value', () => ({
    template: single,
    props: {
      data: testData,
      value: '3',
      disabled: true,
      change: action('change')
    }
  }))
  .add('disableEmptyOption', () => ({
    template: single,
    props: {
      data: testData,
      disableEmptyOption: true,
      change: action('change')
    },
  }))
  .add('multiple', () => ({
    template: multiple,
    props: {
      data: testData,
      showSearch: true,
      change: action('change')
    }
  }))
  .add('multiple value', () => ({
    template: multiple,
    props: {
      data: testData,
      value: ['1', '5', '2'],
      showSearch: true,
      change: action('change')
    }
  }))
  .add('multiple value disabled', () => ({
    template: multiple,
    props: {
      data: testData,
      value: ['1', '5'],
      showSearch: true,
      disabled: true,
      change: action('change')
    }
  }))
  .add('multiple wrong value', () => ({
    template: multiple,
    props: {
      data: testData,
      value: ['1', '5', '7'],
      showSearch: true,
      change: action('change')
    }
  }))
  .add('multiple disableAllOption', () => ({
    template: multiple,
    props: {
      data: testData,
      value: ['1', '5'],
      showSearch: true,
      disableAllOption: true,
      change: action('change')
    }
  }));
